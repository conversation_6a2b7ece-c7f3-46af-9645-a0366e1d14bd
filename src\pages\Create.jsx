import { nanoid } from "nanoid";
import { useContext } from "react";
import { useForm } from "react-hook-form";
import { dataContext } from "../context/RecepiContext";

const Create = () => {
  const {data,setData} = useContext(dataContext);
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm();
  const submitHandler = (recepie) => {
    recepie.id=nanoid();
    setData([...data,recepie]);
    reset();
  };
  return (
    <div className="w-full p-4 flex items-center justify-center flex-col">
      <h1 className="text-4xl font-bold">
        Create your own <span className="text-red-500">recepie</span>{" "}
      </h1>
      <form className="w-full flex flex-col gap-y-4 items-center mt-8" onSubmit={handleSubmit(submitHandler)}>
        <input
          {...register("name", { required: "Enter the Title" })}
          type="text"
          placeholder="Enter the Recepie Name"
          className="w-1/2 p-2 rounded-lg border "
        />
        {errors.name && <p className="text-red-500">{errors.name.message}</p>}
        <textarea
          {...register("description", { required: "Enter the Description" })}
          type="text"
          placeholder="Enter the Recepie Description"
          className="w-1/2 p-2 rounded-lg border "
        />
        {errors.description && <p className="text-red-500">{errors.description.message}</p>}
        <textarea
          {...register("ingredients", { required: "few ingredients are required" })}
          type="text"
          placeholder="Enter the Recepie Ingredients seperated by comma ( , )"
          className="w-1/2 p-2 rounded-lg border "
        />
        {errors.ingredients && <p className="text-red-500">{errors.ingredients.message}</p>}
        <select
          {...register("type", { required: "Enter the Type" })}
          className="w-1/2 p-2 rounded-lg border "
        >
          <option className="text-white bg-gray-800" value="veg">
            Veg
          </option>
          <option className="text-white bg-gray-800" value="non-veg">
            Non-Veg
          </option>
        </select>
        {errors.type && <p className="text-red-500">{errors.type.message}</p>}
        <input
          {...register("chef", { required: "Chef name is required" })}
          type="text"
          placeholder="Enter the name of Chef"
          className="w-1/2 p-2 rounded-lg border "
        />
        {errors.chef && <p className="text-red-500">{errors.chef.message}</p>}
        <input
          {...register("image", { required: "provide image url" })}
          type="url"
          placeholder="Enter the Recepie Image url"
          className="w-1/2 p-2 rounded-lg border "
        />
        {errors.image && <p className="text-red-500">{errors.image.message}</p>}
        <button
          type="submit"
          className="w-1/2 p-2 rounded-lg border bg-red-500 text-white cursor-pointer"
        >
          Submit
        </button>
      </form>
    </div>
  );
};

export default Create;
