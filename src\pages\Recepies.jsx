import React from 'react'
import { useContext } from 'react'
import { dataContext } from '../context/RecepiContext'

const Recepies = () => {
  const {data} = useContext(dataContext);
  return (
    <div>
      {data.map((recepie,index)=>(
        <div key={index}>
          <h1>{recepie.name}</h1>
          <img src={recepie.image} alt={recepie.name} />
          <p>{recepie.description}</p>
          <p>{recepie.ingredients}</p>
          <p>{recepie.chef}</p>
          <p>{recepie.type}</p>
        </div>
      ))}
    </div>
  )
}

export default Recepies
